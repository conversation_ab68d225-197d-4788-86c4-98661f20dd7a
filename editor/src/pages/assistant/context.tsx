import { isCancel, isRequestError, request, RequestConfig, useOptions, useSelector } from '@topwrite/common';
import { pick } from 'lodash';
import {
    createContext,
    Dispatch,
    PropsWithChildren,
    SetStateAction,
    useCallback,
    useContext as useRcContext,
    useEffect,
    useMemo,
    useRef,
    useState
} from 'react';
import useImmer, { Updater } from '../../lib/use-immer';
import useLocalStorageStateWithBook from '../../lib/use-local-storage-state-with-book';
import { Message } from './message-item';
import useMcpServers, { McpServer, McpServerTool } from './use-mcp-servers';

// MCP 工具缓存
const mcpToolsCache = new Map<string, McpServerTool[]>();

export interface Conversation {
    id: string;
    title?: string;
    create_time: string;
    messages: Message[];
}

export type WindowState = 'chat' | 'history' | 'settings';

interface ContextType {
    filename: string | null;
    range?: FileRange | null;
    conversationId?: string;
    setConversationId: Dispatch<SetStateAction<string | undefined>>;
    messages: Message[];
    setMessages: Updater<Message[]>;
    loading: boolean;
    setLoading: Dispatch<SetStateAction<boolean>>;
    send: (query: string) => Promise<void>;
    stop: () => void;
    reset: (conversation?: Conversation) => void;
    windowState: WindowState;
    setWindowState: (state: WindowState) => void;
    authorized: string | boolean;
    builtinTools: BuiltinTool[];
    mcpServers: McpServer[];
    addMcpServer: (server: McpServer) => void;
    deleteMcpServer: (name: string) => void;
    getMcpTools: (server: McpServer) => Promise<McpServerTool[]>;
}

const Context = createContext<ContextType | null>(null);

export function Provider({ children, conversation }: PropsWithChildren<{ conversation?: Conversation }>) {
    const { assistant } = useOptions();
    const { current: filename } = useSelector('workspace');
    const { id, config } = useSelector('book');
    const [windowState, setWindowState] = useState<WindowState>('chat');
    const { servers: mcpServers, addServer: addMcpServer, deleteServer: deleteMcpServer } = useMcpServers();
    const { tools: builtinTools } = assistant;

    // 获取工具配置
    const [builtinEnabledTools] = useLocalStorageStateWithBook<string[] | null>('builtin-enabled-tools', null);

    const getMcpTools = useCallback(async (server: McpServer) => {
        // 生成缓存键，基于服务器名称和URL
        const cacheKey = `${server.name}:${server.url}`;

        // 检查缓存中是否已有数据
        if (mcpToolsCache.has(cacheKey)) {
            return mcpToolsCache.get(cacheKey)!;
        }

        // 如果缓存中没有，则发起请求
        const { data } = await request({
            method: 'post',
            url: `${assistant.base}/mcp/tools`,
            data: server
        });

        // 将结果存入缓存
        mcpToolsCache.set(cacheKey, data);

        return data;
    }, [assistant.base]);

    // 获取启用的内置工具
    const getEnabledBuiltinTools = useCallback(() => {
        return builtinTools.flatMap(tool => {
            if (builtinEnabledTools === null || builtinEnabledTools.includes(`${tool.plugin}.${tool.name}`)) {
                return [pick(tool, ['plugin', 'name', 'args'])];
            }
            return [];
        });
    }, [builtinTools, builtinEnabledTools]);

    // 获取 MCP 服务器配置
    const getMcpServerConfig = useCallback((server: McpServer) => {
        const storageKey = `mcp-${server.name}-enabled-tools`;
        const enabledTools = localStorage.getItem(`editor.${id}.${storageKey}`);
        const parsedEnabledTools = enabledTools ? JSON.parse(enabledTools) : null;

        // 基础配置
        const baseConfig = {
            name: server.name,
            url: server.url,
            ...(server.type && { type: server.type }),
            ...(server.headers && { headers: server.headers })
        };

        // 如果是 null，表示允许所有工具，不需要传 allowed 参数
        if (parsedEnabledTools === null) {
            return baseConfig;
        }

        // 如果是空数组，表示不允许任何工具，忽略这个 MCP 服务器
        if (parsedEnabledTools.length === 0) {
            return null;
        }

        // 如果有具体的工具列表，传递 allowed 参数
        return {
            ...baseConfig,
            allowed: parsedEnabledTools
        };
    }, [id]);

    const authorized = useMemo<string | boolean>(() => {
        if (assistant.authorized) {
            return true;
        }
        return config.getValue(['assistant', 'token']) || false;
    }, [assistant.authorized, config]);

    const getInitialMessages = useCallback((conversation?: Conversation) => {
        const messages: Message[] = [];

        if (conversation) {
            return messages.concat(conversation.messages);
        }
        return messages;
    }, []);

    const [conversationId, setConversationId] = useState<string | undefined>(conversation?.id);
    const conversationRef = useRef(conversationId);

    useEffect(() => {
        conversationRef.current = conversationId;
    }, [conversationId]);

    const [messages, setMessages] = useImmer<Message[]>(() => {
        return getInitialMessages(conversation);
    });

    const controller = useRef<AbortController | null>(null);
    const [loading, setLoading] = useState(false);

    const stop = useCallback(() => {
        if (controller.current) {
            controller.current.abort();
            controller.current = null;
        }
        setLoading(false);
        setMessages((messages) => {
            const message = messages[messages.length - 1];
            if (message && message.loading) {
                message.loading = false;
            }
        });
    }, [setMessages]);

    const send = useCallback(async (query: string) => {
        if (query) {
            if (controller.current) {
                controller.current.abort();
            }
            controller.current = new AbortController();
            setLoading(true);

            const input = {
                query,
                context: filename ? {
                    filename
                } : undefined,
            };

            setMessages((messages) => {
                messages.push(
                    {
                        input: input,
                        output: [],
                        loading: true,
                    },
                );
            });

            try {
                // 获取已启用的工具配置
                const tools = getEnabledBuiltinTools();

                // 获取启用的 MCP 服务器配置
                const mcp = mcpServers.flatMap(tool => {
                    const mcpConfig = getMcpServerConfig(tool);
                    if (mcpConfig) {
                        return [mcpConfig];
                    }
                    return [];
                });

                const data: Record<string, any> = {
                    input,
                    conversation: conversationRef.current,
                    tools,
                    mcp
                };

                if (typeof authorized === 'string') {
                    data.token = authorized;
                }

                const requestConfig: RequestConfig = {
                    method: 'post',
                    url: `${assistant.base}/chat`,
                    data,
                    signal: controller.current.signal,
                    onMessage: (message) => {
                        if (message.data) {
                            if (message.data != '[DONE]') {
                                try {
                                    const event = JSON.parse(message.data);
                                    if (event.conversation) {
                                        conversationRef.current = event.conversation;
                                        setConversationId(event.conversation);
                                    } else {
                                        setMessages((messages) => {
                                            const message = messages[messages.length - 1];
                                            if (message.output) {
                                                if (event.chunks) {

                                                    const chunkIndex: number = event.chunks.index;
                                                    if (!message.output[chunkIndex]) {
                                                        message.output[chunkIndex] = {
                                                            content: '',
                                                        };
                                                    }
                                                    if (event.chunks.error) {
                                                        message.output[chunkIndex].error = event.chunks.error;
                                                    } else if (event.chunks.tools) {
                                                        if (!message.output[chunkIndex].tools) {
                                                            message.output[chunkIndex].tools = [];
                                                        }
                                                        const toolIndex = event.chunks.tools.index;
                                                        if ('response' in event.chunks.tools) {
                                                            message.output[chunkIndex].tools[toolIndex].response = event.chunks.tools.response;
                                                            message.output[chunkIndex].tools[toolIndex].error = event.chunks.tools.error;
                                                            message.output[chunkIndex].tools[toolIndex].content = event.chunks.tools.content;
                                                        } else {
                                                            message.output[chunkIndex].tools[toolIndex] = {
                                                                name: event.chunks.tools.name,
                                                                title: event.chunks.tools.title,
                                                                arguments: event.chunks.tools.arguments
                                                            };
                                                        }
                                                    } else if (event.chunks.content) {
                                                        if (typeof event.chunks.content === 'object') {
                                                            if (!Array.isArray(message.output[chunkIndex].content)) {
                                                                message.output[chunkIndex].content = [];
                                                            }
                                                            const contentIndex: number = event.chunks.content.index;
                                                            const contentValue = event.chunks.content.value;

                                                            if (typeof contentValue === 'string') {
                                                                if (!message.output[chunkIndex].content[contentIndex]) {
                                                                    message.output[chunkIndex].content[contentIndex] = '';
                                                                }
                                                                message.output[chunkIndex].content[contentIndex] += contentValue;
                                                            } else {
                                                                message.output[chunkIndex].content[contentIndex] = contentValue;
                                                            }
                                                        } else {
                                                            message.output[chunkIndex].content += event.chunks.content;
                                                        }
                                                    }
                                                } else if (event.id) {
                                                    message.id = event.id;
                                                }
                                            }
                                        });
                                    }
                                } catch (e) {
                                    console.error(e);
                                }
                            } else {
                                setMessages((messages) => {
                                    const message = messages[messages.length - 1];
                                    message.loading = false;
                                });
                            }
                        }
                    },
                };

                await request(requestConfig);
            } catch (e) {
                if (isCancel(e)) {
                    setMessages((messages) => {
                        const message = messages[messages.length - 1];
                        message.loading = false;
                    });
                } else {
                    let errors = '未知错误';
                    if (isRequestError(e)) {
                        if (e.response?.status == 401) {
                            errors = '未授权或授权已过期，请刷新页面后重试';
                        } else {
                            if (typeof e.errors === 'string') {
                                errors = e.errors;
                            } else {
                                errors = Object.values(e.errors).join('\n');
                            }
                        }
                    }
                    setMessages((messages) => {
                        const message = messages[messages.length - 1];
                        if (message.output) {
                            if (message.output.length === 0) {
                                message.output = [{
                                    content: `[${errors}]`,
                                    tools: []
                                }];
                            } else {
                                message.output[message.output.length - 1].content = `[${errors}]`;
                            }
                        }
                        message.loading = false;
                    });
                }
            }

            setLoading(false);
        }
    }, [assistant.base, filename, conversationRef, setMessages, setLoading, authorized, getEnabledBuiltinTools, getMcpServerConfig, builtinTools, mcpServers]);

    const reset = useCallback((conversation?: Conversation) => {
        if (!loading) {
            setConversationId(conversation?.id);
            setMessages(getInitialMessages(conversation));
        }
    }, [loading, getInitialMessages]);

    return <Context.Provider value={{
        filename,
        range: undefined, // 这里可以根据需要设置当前的range状态
        conversationId,
        setConversationId,
        messages,
        setMessages,
        loading,
        setLoading,
        send,
        stop,
        reset,
        windowState,
        setWindowState,
        authorized,
        builtinTools,
        mcpServers,
        addMcpServer,
        deleteMcpServer,
        getMcpTools
    }}>
        {children}
    </Context.Provider>;
}

export function useContext() {
    const context = useRcContext(Context);
    if (!context) {
        throw new Error('useContext must be used within a Provider');
    }
    return context;
}
